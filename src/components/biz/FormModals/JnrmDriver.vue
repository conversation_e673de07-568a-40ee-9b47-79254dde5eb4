<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="grid grid-cols-2 gap-x-base">
					<XFormItem label="姓名" prop="name">
						<XInput v-model="model.name" placeholder="请输入姓名" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="性别" prop="gender">
						<XSelect v-model="model.gender" tag-group-name="性别" />
					</XFormItem>
					<XFormItem label="手机号" prop="phone">
						<XInput v-model="model.phone" placeholder="请输入手机号" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="身份证号码" prop="idCard">
						<XInput v-model="model.idCard" placeholder="请输入身份证号码" @keyup.enter.stop="handleSubmit" />
					</XFormItem>

					<XFormItem label="民族" prop="ethnicity">
						<XInput v-model="model.ethnicity" placeholder="请输入民族" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="出生日期" prop="birthDate">
						<XDateTimePicker v-model="model.birthDate" type="date" placeholder="选择出生日期" />
					</XFormItem>
					<XFormItem label="驾驶证" prop="driverLicenseUrl">
						<XFileUpload v-model="model.driverLicenseUrl" placeholder="请输入驾驶证URL" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="健康证" prop="healthCertificateUrl">
						<XFileUpload v-model="model.healthCertificateUrl" placeholder="请输入健康证URL" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
				</div>
				<div class="grid grid-cols-2 gap-x-base">
					<XFormItem label="身份证(国徽)" prop="idCardGhUrl">
						<XFileUpload v-model="model.idCardGhUrl" placeholder="请输入" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="身份证(人像)" prop="idCardRxUrl">
						<XFileUpload v-model="model.idCardRxUrl" placeholder="请输入" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	name: undefined,
	gender: undefined,
	ethnicity: undefined,
	phone: undefined,
	idCard: undefined,
	birthDate: undefined,
	driverLicenseUrl: undefined,
	healthCertificateUrl: undefined,
	idCardRxUrl: undefined,
	idCardGhUrl: undefined,
})
const rules = reactive({
	name: [{ required: true, message: '姓名不能为空' }],
	gender: [{ required: true, message: '性别（1：男，2：女）不能为空' }],
	phone: [{ required: true, message: '手机号不能为空' }],
	idCard: [{ required: true, message: '身份证号码不能为空' }],
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
const requestLoading = ref(false)
let run, data
const computedLoading = computed(() => requestLoading?.value || detailLoading.value)
const params = ref({})
function open(_type, _id, _params) {
	model.value = {}
	params.value = _params
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading, data: _data } = xUseRequest(BIZ_JnrmDriver_APIS[_type], model)
	run = _run
	// 监听xUseRequest返回的loading状态，同步到组件级别的loading
	watchEffect(() => {
		requestLoading.value = loading.value
	})
	data = _data
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_JnrmDriver_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	if (!visible.value) return
	await run()
	visible.value = false
	if (data && type.value == 'create') {
		model.value.id = data
	}
	emit('success', model.value)
}
</script>

<style scoped></style>
