<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="grid grid-cols-2 gap-x-base">
					<XFormItem label="车牌号" prop="licensePlate">
						<XInput v-model="model.licensePlate" placeholder="请输入车牌号" @keyup.enter.stop="handleSubmit" />
					</XFormItem>

					<XFormItem label="下次年检日期" prop="nextInspectionDate">
						<XDateTimePicker v-model="model.nextInspectionDate" type="date" placeholder="选择下次年检日期" />
					</XFormItem>
					<XFormItem label="车型" prop="vehicleType">
						<XInput v-model="model.vehicleType" placeholder="请选择车型" />
					</XFormItem>
					<XFormItem label="载重(吨)" prop="loadCapacity">
						<XInput v-model="model.loadCapacity" placeholder="请输入吨位" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="车辆品牌" prop="vehicleBrand">
						<XInput v-model="model.vehicleBrand" placeholder="请输入车辆品牌" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="禁用" prop="isDisabled">
						<XSelect v-model="model.isDisabled" tag-group-name="是否" tag-value-format="bool" />
					</XFormItem>
				</div>
				<XFormItem label="行驶证" prop="licenseUrl">
					<XFileUpload v-model="model.licenseUrl" placeholder="请输入行驶证URL" @keyup.enter.stop="handleSubmit" />
				</XFormItem>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	licensePlate: undefined,
	licenseUrl: undefined,
	nextInspectionDate: undefined,
	vehicleType: undefined,
	loadCapacity: undefined,
	vehicleBrand: undefined,
	isDisabled: undefined,
})
const rules = reactive({
	licensePlate: [{ required: true, message: '车牌号不能为空' }],
	isDisabled: [{ required: true, message: '是否禁用不能为空' }],
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
const requestLoading = ref(false)
let run, data
const computedLoading = computed(() => requestLoading?.value || detailLoading.value)
const params = ref({})
function open(_type, _id, _params) {
	model.value = { isDisabled: false }
	params.value = _params
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading, data: _data } = xUseRequest(BIZ_JnrmVehicle_APIS[_type], model)
	run = _run
	// 监听xUseRequest返回的loading状态，同步到组件级别的loading
	watchEffect(() => {
		requestLoading.value = loading.value
	})
	data = _data
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_JnrmVehicle_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	if (!visible.value) return
	await run()
	visible.value = false
	if (data && type.value == 'create') {
		model.value.id = data
	}
	emit('success', model.value)
}
</script>

<style scoped></style>
