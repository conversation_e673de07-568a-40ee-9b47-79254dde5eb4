<template>
	<div
		ref="containerRef"
		v-loading="isLoading"
		class="relative overflow-hidden border-box"
		:class="attrs.class"
		:style="[attrs.style, containerStyle]"
	>
		<!-- 图片主体 -->
		<img
			v-show="!isError && !isLoading"
			:src="src"
			class="cursor-zoom-in"
			:class="imageClasses"
			v-bind="imgAttrs"
			@click="ximagepreview.preview(src)"
			@load="handleLoad"
			@error="handleError"
		/>
		<!-- 错误状态 -->
		<div v-if="isError" class="absolute inset-0 flex items-center justify-center">
			<slot name="error">
				<div class="hfull wfull flex items-center justify-center text-aid text-lg">
					<XIconsImageBroken class="w-60%" />
				</div>
			</slot>
		</div>
	</div>
</template>

<script setup lang="ts">
const props = defineProps({
	src: {
		type: String,
		required: true,
	},
	mode: {
		type: String as PropType<'scaleToFill' | 'aspectFit' | 'aspectFill' | 'widthFix' | 'heightFix' | 'top' | 'bottom' | 'left' | 'right' | 'center'>,
		default: 'aspectFit',
	},
})

const attrs = useAttrs()
const containerRef = ref(null)
const isLoading = ref(!!props.src)
const isError = ref(!props.src)
const containerStyle = ref({})

// 处理图片属性
const imgAttrs = computed(() => {
	const { class: _, style: __, ...rest } = attrs
	return rest
})

// 图片类名计算
const imageClasses = computed(() => {
	const classes = []
	const [mainMode, positionMode] = props.mode.split(' ')

	switch (mainMode) {
		case 'scaleToFill':
			classes.push('w-full h-full object-fill')
			break
		case 'aspectFit':
			classes.push('w-full h-full object-contain')
			break
		case 'aspectFill':
			classes.push('w-full h-full object-cover')
			break
		case 'widthFix':
			classes.push('w-full h-auto')
			break
		case 'heightFix':
			classes.push('h-full w-auto')
			break
		default:
			if (positionMode) {
				classes.push(`w-full h-full object-cover object-${mainMode}-${positionMode}`)
			} else {
				classes.push(`w-full h-full object-cover object-${mainMode}`)
			}
	}

	return classes
})

// 图片加载成功
const handleLoad = (e) => {
	isLoading.value = false
	if (props.mode === 'widthFix' || props.mode === 'heightFix') {
		updateContainerSize(e.target)
	}
}

// 更新容器尺寸（针对 widthFix/heightFix 模式）
const updateContainerSize = (img) => {
	if (!containerRef.value) return

	if (props.mode === 'widthFix') {
		containerStyle.value = {
			height: `${(img.naturalHeight / img.naturalWidth) * containerRef.value.offsetWidth}px`,
		}
	} else if (props.mode === 'heightFix') {
		containerStyle.value = {
			width: `${(img.naturalWidth / img.naturalHeight) * containerRef.value.offsetHeight}px`,
		}
	}
}

// 图片加载失败
const handleError = () => {
	isLoading.value = false
	isError.value = true
}

// 监听 src 变化
watch(
	() => props.src,
	(nv) => {
		if (nv) {
			isLoading.value = true
			isError.value = false
			containerStyle.value = {}
		} else {
			handleError()
		}
	},
)
</script>
