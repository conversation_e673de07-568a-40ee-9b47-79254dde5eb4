<route lang="json">
{
	"meta": {
		"title": "驾驶员"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	name: null,
	gender: null,
	ethnicity: null,
	phone: null,
	idCard: null,
	birthDate: [],
	driverLicenseUrl: null,
	healthCertificateUrl: null,
	createTime: [],
	...computedParams.value,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_JnrmDriver_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_JnrmDriver_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id, computedParams.value)
}

const columns = [
	{
		label: '姓名',
		prop: 'name',
	},
	{
		label: '性别',
		prop: 'gender',
	},
	{
		label: '民族',
		prop: 'ethnicity',
	},
	{
		label: '手机号',
		prop: 'phone',
	},
	{
		label: '身份证号',
		prop: 'idCard',
	},
	{
		label: '行驶证',
		prop: 'driverLicenseUrl',
		type: 'img',
	},
	{
		label: '健康证',
		prop: 'healthCertificateUrl',
		type: 'img',
	},
	{
		label: '身份证(国徽)',
		prop: 'idCardGhUrl',
		type: 'img',
	},
	{
		label: '身份证(人像)',
		prop: 'idCardRxUrl',
		type: 'img',
	},
	{
		label: '出生日期',
		prop: 'birthDate',
		width: 150,
		formatter: X_DATE_UTILS.formatOnlyDate,
	},
	{
		label: '操作',
		prop: 'action',
	},
]
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsJnrmDriver ref="modalRef" @success="run()" />
		<BizCardsQuery>
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="手机号" prop="phone">
					<XInput v-model="queryModel.phone" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="姓名" prop="name">
					<XInput v-model="queryModel.name" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="身份证" prop="idCard">
					<XInput v-model="queryModel.idCard" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="性别" prop="gender">
					<XSelect v-model="queryModel.gender" placeholder="请选择搜索" tag-group-name="性别" />
				</XFormItem>
			</BizFormQueryForm>
			<div class="flex items-center justify-end gap-xs">
				<BizButtonsReset @click="reset" />
				<BizButtonsQuery @click="run" />
			</div>
		</BizCardsQuery>
		<div class="flex items-center justify-between py-xs">
			<div></div>
			<div class="flex items-center">
				<BizButtonsCreate v-if="hasPermission('x:jnrm-driver:create')" @click="handleOpenModal('create')" />
			</div>
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<BizButtonsUpdate v-if="hasPermission('x:jnrm-driver:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('x:jnrm-driver:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
